@echo off
echo 编译 JTAG Writer...

REM 创建目录
if not exist build\obj\core mkdir build\obj\core
if not exist build\obj\network mkdir build\obj\network
if not exist build\obj\config mkdir build\obj\config
if not exist build\obj\ui mkdir build\obj\ui
if not exist build\obj\utils mkdir build\obj\utils

REM 编译源文件
echo 编译 main.c...
gcc -Wall -Wextra -std=c99 -Iinclude -c src\main.c -o build\obj\main.o
if %errorlevel% neq 0 goto error

echo 编译 jtag_writer.c...
gcc -Wall -Wextra -std=c99 -Iinclude -c src\core\jtag_writer.c -o build\obj\core\jtag_writer.o
if %errorlevel% neq 0 goto error

echo 编译 flash_operations.c...
gcc -Wall -Wextra -std=c99 -Iinclude -c src\core\flash_operations.c -o build\obj\core\flash_operations.o
if %errorlevel% neq 0 goto error

echo 编译 openocd_client.c...
gcc -Wall -Wextra -std=c99 -Iinclude -c src\network\openocd_client.c -o build\obj\network\openocd_client.o
if %errorlevel% neq 0 goto error

echo 编译 config_manager.c...
gcc -Wall -Wextra -std=c99 -Iinclude -c src\config\config_manager.c -o build\obj\config\config_manager.o
if %errorlevel% neq 0 goto error

echo 编译 cli_interface.c...
gcc -Wall -Wextra -std=c99 -Iinclude -c src\ui\cli_interface.c -o build\obj\ui\cli_interface.o
if %errorlevel% neq 0 goto error

echo 编译 logger.c...
gcc -Wall -Wextra -std=c99 -Iinclude -c src\utils\logger.c -o build\obj\utils\logger.o
if %errorlevel% neq 0 goto error

REM 链接
echo 链接可执行文件...
gcc build\obj\main.o build\obj\core\jtag_writer.o build\obj\core\flash_operations.o build\obj\network\openocd_client.o build\obj\config\config_manager.o build\obj\ui\cli_interface.o build\obj\utils\logger.o -o build\jtag_writer.exe -lws2_32
if %errorlevel% neq 0 goto error

echo 编译成功！
echo 可执行文件: build\jtag_writer.exe
goto end

:error
echo 编译失败！
exit /b 1

:end
